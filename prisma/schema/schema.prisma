generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  BUYER  @map("buyer")
  SELLER @map("seller")
  ADMIN  @map("admin")
}

enum Status {
  ACTIVE    @map("active")
  INACTIVE  @map("inactive")
  SUSPENDED @map("suspended")
}

enum CarCondition {
  NEW       @map("new")
  USED      @map("used")
  CERTIFIED @map("certified")
}

enum FuelType {
  GASOLINE @map("gasoline")
  DIESEL   @map("diesel")
  ELECTRIC @map("electric")
  HYBRID   @map("hybrid")
}

enum TransmissionType {
  MANUAL         @map("manual")
  AUTOMATIC      @map("automatic")
  SEMI_AUTOMATIC @map("semi-automatic")
}

enum DriveType {
  FWD    @map("fwd")
  RWD    @map("rwd")
  AWD    @map("awd")
  FOURWD @map("4wd")
}

enum ListingType {
  CAR       @map("car")
  ACCESSORY @map("accessory")
}

enum ListingStatus {
  ACTIVE  @map("active")
  PENDING @map("pending")
  SOLD    @map("sold")
  DRAFT   @map("draft")
}

enum PostStatus {
  DRAFT     @map("draft")
  PUBLISHED @map("published")
  ARCHIVED  @map("archived")
}

enum ProductStatus {
  ACTIVE   @map("active")
  INACTIVE @map("inactive")
  DRAFT    @map("draft")
}

enum StockStatus {
  INSTOCK     @map("instock")
  OUTOFSTOCK  @map("outofstock")
  ONBACKORDER @map("onbackorder")
}

enum OrderStatus {
  PENDING    @map("pending")
  PROCESSING @map("processing")
  SHIPPED    @map("shipped")
  DELIVERED  @map("delivered")
  CANCELLED  @map("cancelled")
}

enum PaymentStatus {
  PENDING  @map("pending")
  PAID     @map("paid")
  FAILED   @map("failed")
  REFUNDED @map("refunded")
}

enum BillingPeriod {
  MONTHLY @map("monthly")
  YEARLY  @map("yearly")
}

enum SubscriptionStatus {
  ACTIVE    @map("active")
  CANCELLED @map("cancelled")
  EXPIRED   @map("expired")
}

enum SettingType {
  STRING  @map("string")
  NUMBER  @map("number")
  BOOLEAN @map("boolean")
  JSON    @map("json")
}

enum ItemCondition {
  NEW         @map("new")
  USED        @map("used")
  REFURBISHED @map("refurbished")
}

// Models
model User {
  id             BigInt    @id @default(autoincrement())
  email          String    @unique @db.VarChar(255)
  passwordHash   String    @map("password_hash") @db.VarChar(255)
  firstName      String    @map("first_name") @db.VarChar(100)
  lastName       String    @map("last_name") @db.VarChar(100)
  phone          String?   @db.VarChar(20)
  profilePicture String?   @map("profile_picture") @db.VarChar(500)
  location       String?   @db.VarChar(255)
  bio            String?   @db.Text
  role           Role      @default(BUYER)
  status         Status    @default(ACTIVE)
  emailVerified  Boolean   @default(false) @map("email_verified")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  lastLogin      DateTime? @map("last_login")

  // Relations
  sentMessages     Message[]          @relation("SentMessages")
  receivedMessages Message[]          @relation("ReceivedMessages")
  ShoppingCart     ShoppingCart[]
  BlogComment      BlogComment[]
  BlogPost         BlogPost[]
  Review           Review[]
  Order            Order[]
  CarListing       CarListing[]
  LoanCalculation  LoanCalculation[]
  MessageThread    MessageThread[]
  MessageThread    MessageThread[]
  Notification     Notification[]
  SearchHistory    SearchHistory[]
  UserSocialLink   UserSocialLink[]
  UserFavorite     UserFavorite[]
  UserSubscription UserSubscription[]
  UserView         UserView[]

  @@index([email])
  @@index([role])
  @@index([status])
  @@map("users")
}

model Message {
  id          BigInt   @id @default(autoincrement())
  senderId    BigInt   @map("sender_id")
  recipientId BigInt   @map("recipient_id")
  listingId   BigInt?  @map("listing_id")
  subject     String?  @db.VarChar(255)
  message     String   @db.Text
  isRead      Boolean  @default(false) @map("is_read")
  createdAt   DateTime @default(now()) @map("created_at")

  sender        User            @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  recipient     User            @relation("ReceivedMessages", fields: [recipientId], references: [id], onDelete: Cascade)
  CarListing    CarListing?     @relation(fields: [carListingId], references: [id])
  carListingId  BigInt?
  MessageThread MessageThread[]

  @@index([senderId])
  @@index([recipientId])
  @@index([listingId])
  @@index([isRead])
  @@index([createdAt])
  @@map("messages")
}
