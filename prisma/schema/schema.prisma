generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum Role {
  BUYER  @map("buyer")
  SELLER @map("seller")
  ADMIN  @map("admin")
}

enum Status {
  ACTIVE    @map("active")
  INACTIVE  @map("inactive")
  SUSPENDED @map("suspended")
}

enum CarCondition {
  NEW       @map("new")
  USED      @map("used")
  CERTIFIED @map("certified")
}

enum FuelType {
  GASOLINE @map("gasoline")
  DIESEL   @map("diesel")
  ELECTRIC @map("electric")
  HYBRID   @map("hybrid")
}

enum TransmissionType {
  MANUAL         @map("manual")
  AUTOMATIC      @map("automatic")
  SEMI_AUTOMATIC @map("semi-automatic")
}

enum DriveType {
  FWD    @map("fwd")
  RWD    @map("rwd")
  AWD    @map("awd")
  FOURWD @map("4wd")
}

enum ListingType {
  CAR       @map("car")
  ACCESSORY @map("accessory")
}

enum ListingStatus {
  ACTIVE  @map("active")
  PENDING @map("pending")
  SOLD    @map("sold")
  DRAFT   @map("draft")
}

enum PostStatus {
  DRAFT     @map("draft")
  PUBLISHED @map("published")
  ARCHIVED  @map("archived")
}

enum ProductStatus {
  ACTIVE   @map("active")
  INACTIVE @map("inactive")
  DRAFT    @map("draft")
}

enum StockStatus {
  INSTOCK     @map("instock")
  OUTOFSTOCK  @map("outofstock")
  ONBACKORDER @map("onbackorder")
}

enum OrderStatus {
  PENDING    @map("pending")
  PROCESSING @map("processing")
  SHIPPED    @map("shipped")
  DELIVERED  @map("delivered")
  CANCELLED  @map("cancelled")
}

enum PaymentStatus {
  PENDING  @map("pending")
  PAID     @map("paid")
  FAILED   @map("failed")
  REFUNDED @map("refunded")
}

enum BillingPeriod {
  MONTHLY @map("monthly")
  YEARLY  @map("yearly")
}

enum SubscriptionStatus {
  ACTIVE    @map("active")
  CANCELLED @map("cancelled")
  EXPIRED   @map("expired")
}

enum SettingType {
  STRING  @map("string")
  NUMBER  @map("number")
  BOOLEAN @map("boolean")
  JSON    @map("json")
}

enum ItemCondition {
  NEW         @map("new")
  USED        @map("used")
  REFURBISHED @map("refurbished")
}

// Main Models
model User {
  id                 String    @id @default(cuid())
  email              String    @unique @db.VarChar(255)
  passwordHash       String    @map("password_hash") @db.VarChar(255)
  firstName          String    @map("first_name") @db.VarChar(100)
  lastName           String    @map("last_name") @db.VarChar(100)
  phone              String?   @db.VarChar(20)
  profilePicture     String?   @map("profile_picture") @db.VarChar(500)
  location           String?   @db.VarChar(255)
  bio                String?   @db.Text
  role               Role      @default(BUYER)
  status             Status    @default(ACTIVE)
  emailVerified      Boolean   @default(false) @map("email_verified")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  lastLogin          DateTime? @map("last_login")
  refreshToken       String?   @map("refresh_token") @db.VarChar(255)
  refreshTokenExpiry DateTime? @map("refresh_token_expiry")

  googleId         String?            @map("google_id") @db.VarChar(255)
  socialLinks      UserSocialLink[]
  carListings      CarListing[]
  favorites        UserFavorite[]
  views            UserView[]
  sentMessages     Message[]          @relation("SentMessages")
  receivedMessages Message[]          @relation("ReceivedMessages")
  user1Threads     MessageThread[]    @relation("User1Threads")
  user2Threads     MessageThread[]    @relation("User2Threads")
  blogPosts        BlogPost[]
  blogComments     BlogComment[]
  shoppingCart     ShoppingCart[]
  orders           Order[]
  reviews          Review[]
  searchHistory    SearchHistory[]
  notifications    Notification[]
  subscriptions    UserSubscription[]
  loanCalculations LoanCalculation[]
  accessories      Accessory[]

  @@index([email])
  @@index([role])
  @@index([status])
  @@map("users")
}

model UserSocialLink {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  platform  String   @db.VarChar(50)
  url       String   @db.VarChar(500)
  createdAt DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("user_social_links")
}

model CarMake {
  id        String   @id @default(cuid())
  name      String   @unique @db.VarChar(100)
  logo      String?  @db.VarChar(500)
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")

  models                   CarModel[]
  listings                 CarListing[]
  accessoryCompatibilities AccessoryCompatibility[]

  @@index([name])
  @@index([isActive])
  @@map("car_makes")
}

model CarModel {
  id        String   @id @default(cuid())
  makeId    String   @map("make_id")
  name      String   @db.VarChar(100)
  yearStart Int?     @map("year_start")
  yearEnd   Int?     @map("year_end")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")

  make                     CarMake                  @relation(fields: [makeId], references: [id], onDelete: Cascade)
  listings                 CarListing[]
  accessoryCompatibilities AccessoryCompatibility[]

  @@index([makeId])
  @@index([name])
  @@index([isActive])
  @@map("car_models")
}

model CarListing {
  id             String           @id @default(cuid())
  userId         String           @map("user_id")
  title          String           @db.VarChar(255)
  description    String?          @db.Text
  makeId         String           @map("make_id")
  modelId        String           @map("model_id")
  year           Int
  condition      CarCondition
  price          Decimal          @db.Decimal(12, 2)
  mileage        Int?
  fuelType       FuelType         @map("fuel_type")
  transmission   TransmissionType
  driveType      DriveType        @map("drive_type")
  engineSize     Decimal?         @map("engine_size") @db.Decimal(3, 1)
  cylinders      Int?
  doors          Int?
  seats          Int?
  color          String?          @db.VarChar(50)
  interiorColor  String?          @map("interior_color") @db.VarChar(50)
  vin            String?          @unique @db.VarChar(17)
  primaryImage   String?          @map("primary_image") @db.VarChar(500)
  galleryImages  Json?            @map("gallery_images")
  videos         Json?
  listingType    ListingType      @default(CAR) @map("listing_type")
  status         ListingStatus    @default(PENDING)
  featured       Boolean          @default(false)
  viewsCount     Int              @default(0) @map("views_count")
  favoritesCount Int              @default(0) @map("favorites_count")
  location       String?          @db.VarChar(255)
  latitude       Decimal?         @db.Decimal(10, 8)
  longitude      Decimal?         @db.Decimal(11, 8)
  contactPhone   String?          @map("contact_phone") @db.VarChar(20)
  contactEmail   String?          @map("contact_email") @db.VarChar(255)
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")

  user           User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  make           CarMake             @relation(fields: [makeId], references: [id])
  model          CarModel            @relation(fields: [modelId], references: [id])
  features       CarListingFeature[]
  favorites      UserFavorite[]
  views          UserView[]
  messages       Message[]
  messageThreads MessageThread[]
  reviews        Review[]
  contactForms   ContactForm[]

  @@index([userId])
  @@index([makeId, modelId])
  @@index([price])
  @@index([year])
  @@index([status])
  @@index([featured])
  @@index([listingType])
  @@index([latitude, longitude])
  @@map("car_listings")
}

model CarFeature {
  id        String   @id @default(cuid())
  name      String   @unique @db.VarChar(100)
  category  String   @db.VarChar(50)
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")

  listings CarListingFeature[]

  @@index([category])
  @@index([isActive])
  @@map("car_features")
}

model CarListingFeature {
  id        String   @id @default(cuid())
  listingId String   @map("listing_id")
  featureId String   @map("feature_id")
  createdAt DateTime @default(now()) @map("created_at")

  listing CarListing @relation(fields: [listingId], references: [id], onDelete: Cascade)
  feature CarFeature @relation(fields: [featureId], references: [id], onDelete: Cascade)

  @@unique([listingId, featureId])
  @@index([listingId])
  @@index([featureId])
  @@map("car_listing_features")
}

model UserFavorite {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  listingId String   @map("listing_id")
  createdAt DateTime @default(now()) @map("created_at")

  user    User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  listing CarListing @relation(fields: [listingId], references: [id], onDelete: Cascade)

  @@unique([userId, listingId])
  @@index([userId])
  @@index([listingId])
  @@map("user_favorites")
}

model UserView {
  id        String   @id @default(cuid())
  userId    String?  @map("user_id")
  listingId String   @map("listing_id")
  ipAddress String?  @map("ip_address") @db.VarChar(45)
  userAgent String?  @map("user_agent") @db.Text
  viewedAt  DateTime @default(now()) @map("viewed_at")

  user    User?      @relation(fields: [userId], references: [id], onDelete: SetNull)
  listing CarListing @relation(fields: [listingId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([listingId])
  @@index([viewedAt])
  @@map("user_views")
}

model Message {
  id          String   @id @default(cuid())
  senderId    String   @map("sender_id")
  recipientId String   @map("recipient_id")
  listingId   String?  @map("listing_id")
  subject     String?  @db.VarChar(255)
  message     String   @db.Text
  isRead      Boolean  @default(false) @map("is_read")
  createdAt   DateTime @default(now()) @map("created_at")

  sender    User        @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  recipient User        @relation("ReceivedMessages", fields: [recipientId], references: [id], onDelete: Cascade)
  listing   CarListing? @relation(fields: [listingId], references: [id], onDelete: SetNull)

  @@index([senderId])
  @@index([recipientId])
  @@index([listingId])
  @@index([isRead])
  @@index([createdAt])
  @@map("messages")
}

model MessageThread {
  id            String    @id @default(cuid())
  user1Id       String    @map("user1_id")
  user2Id       String    @map("user2_id")
  listingId     String?   @map("listing_id")
  lastMessageId String?   @map("last_message_id")
  lastMessageAt DateTime? @map("last_message_at")
  createdAt     DateTime  @default(now()) @map("created_at")

  user1   User        @relation("User1Threads", fields: [user1Id], references: [id], onDelete: Cascade)
  user2   User        @relation("User2Threads", fields: [user2Id], references: [id], onDelete: Cascade)
  listing CarListing? @relation(fields: [listingId], references: [id], onDelete: SetNull)

  @@unique([user1Id, user2Id, listingId])
  @@index([user1Id])
  @@index([user2Id])
  @@index([lastMessageAt])
  @@map("message_threads")
}

model BlogCategory {
  id          String   @id @default(cuid())
  name        String   @unique @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")

  posts BlogPost[]

  @@index([slug])
  @@index([isActive])
  @@map("blog_categories")
}

model BlogPost {
  id              String     @id @default(cuid())
  authorId        String     @map("author_id")
  categoryId      String?    @map("category_id")
  title           String     @db.VarChar(255)
  slug            String     @unique @db.VarChar(255)
  excerpt         String?    @db.Text
  content         String     @db.Text
  featuredImage   String?    @map("featured_image") @db.VarChar(500)
  status          PostStatus @default(DRAFT)
  publishedAt     DateTime?  @map("published_at")
  viewsCount      Int        @default(0) @map("views_count")
  commentsCount   Int        @default(0) @map("comments_count")
  metaTitle       String?    @map("meta_title") @db.VarChar(255)
  metaDescription String?    @map("meta_description") @db.Text
  createdAt       DateTime   @default(now()) @map("created_at")
  updatedAt       DateTime   @updatedAt @map("updated_at")

  author   User          @relation(fields: [authorId], references: [id], onDelete: Cascade)
  category BlogCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  comments BlogComment[]
  tags     BlogPostTag[]

  @@index([authorId])
  @@index([categoryId])
  @@index([slug])
  @@index([status])
  @@index([publishedAt])
  @@map("blog_posts")
}

model BlogTag {
  id        String   @id @default(cuid())
  name      String   @unique @db.VarChar(50)
  slug      String   @unique @db.VarChar(50)
  createdAt DateTime @default(now()) @map("created_at")

  posts BlogPostTag[]

  @@index([slug])
  @@map("blog_tags")
}

model BlogPostTag {
  id        String   @id @default(cuid())
  postId    String   @map("post_id")
  tagId     String   @map("tag_id")
  createdAt DateTime @default(now()) @map("created_at")

  post BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag  BlogTag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([postId, tagId])
  @@index([postId])
  @@index([tagId])
  @@map("blog_post_tags")
}

model BlogComment {
  id         String   @id @default(cuid())
  postId     String   @map("post_id")
  userId     String?  @map("user_id")
  parentId   String?  @map("parent_id")
  name       String?  @db.VarChar(100)
  email      String?  @db.VarChar(255)
  content    String   @db.Text
  isApproved Boolean  @default(false) @map("is_approved")
  createdAt  DateTime @default(now()) @map("created_at")

  post    BlogPost      @relation(fields: [postId], references: [id], onDelete: Cascade)
  user    User?         @relation(fields: [userId], references: [id], onDelete: SetNull)
  parent  BlogComment?  @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies BlogComment[] @relation("CommentReplies")

  @@index([postId])
  @@index([userId])
  @@index([parentId])
  @@index([isApproved])
  @@map("blog_comments")
}

model Product {
  id               String        @id @default(cuid())
  name             String        @db.VarChar(255)
  slug             String        @unique @db.VarChar(255)
  description      String?       @db.Text
  shortDescription String?       @map("short_description") @db.Text
  price            Decimal       @db.Decimal(10, 2)
  salePrice        Decimal?      @map("sale_price") @db.Decimal(10, 2)
  sku              String?       @unique @db.VarChar(100)
  stockQuantity    Int           @default(0) @map("stock_quantity")
  manageStock      Boolean       @default(true) @map("manage_stock")
  stockStatus      StockStatus   @default(INSTOCK) @map("stock_status")
  weight           Decimal?      @db.Decimal(8, 2)
  dimensions       String?       @db.VarChar(100)
  featuredImage    String?       @map("featured_image") @db.VarChar(500)
  galleryImages    Json?         @map("gallery_images")
  status           ProductStatus @default(ACTIVE)
  createdAt        DateTime      @default(now()) @map("created_at")
  updatedAt        DateTime      @updatedAt @map("updated_at")

  cartItems  ShoppingCart[]
  orderItems OrderItem[]
  reviews    Review[]

  @@index([slug])
  @@index([sku])
  @@index([status])
  @@index([stockStatus])
  @@map("products")
}

model ShoppingCart {
  id        String   @id @default(cuid())
  userId    String?  @map("user_id")
  sessionId String?  @map("session_id") @db.VarChar(255)
  productId String   @map("product_id")
  quantity  Int      @default(1)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user    User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([sessionId])
  @@index([productId])
  @@map("shopping_cart")
}

model Order {
  id              String        @id @default(cuid())
  userId          String?       @map("user_id")
  orderNumber     String        @unique @map("order_number") @db.VarChar(50)
  status          OrderStatus   @default(PENDING)
  subtotal        Decimal       @db.Decimal(10, 2)
  taxAmount       Decimal       @default(0) @map("tax_amount") @db.Decimal(10, 2)
  shippingAmount  Decimal       @default(0) @map("shipping_amount") @db.Decimal(10, 2)
  totalAmount     Decimal       @map("total_amount") @db.Decimal(10, 2)
  paymentStatus   PaymentStatus @default(PENDING) @map("payment_status")
  paymentMethod   String?       @map("payment_method") @db.VarChar(50)
  shippingAddress Json?         @map("shipping_address")
  billingAddress  Json?         @map("billing_address")
  notes           String?       @db.Text
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")

  user  User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  items OrderItem[]

  @@index([userId])
  @@index([orderNumber])
  @@index([status])
  @@index([paymentStatus])
  @@map("orders")
}

model OrderItem {
  id        String   @id @default(cuid())
  orderId   String   @map("order_id")
  productId String   @map("product_id")
  quantity  Int
  price     Decimal  @db.Decimal(10, 2)
  total     Decimal  @db.Decimal(10, 2)
  createdAt DateTime @default(now()) @map("created_at")

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([productId])
  @@map("order_items")
}

model Review {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  listingId    String?  @map("listing_id")
  productId    String?  @map("product_id")
  rating       Int
  title        String?  @db.VarChar(255)
  content      String?  @db.Text
  isApproved   Boolean  @default(false) @map("is_approved")
  helpfulCount Int      @default(0) @map("helpful_count")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  user    User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  listing CarListing? @relation(fields: [listingId], references: [id], onDelete: Cascade)
  product Product?    @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([listingId])
  @@index([productId])
  @@index([rating])
  @@index([isApproved])
  @@map("reviews")
}

model SearchHistory {
  id           String   @id @default(cuid())
  userId       String?  @map("user_id")
  searchQuery  String   @map("search_query") @db.VarChar(500)
  filters      Json?
  resultsCount Int?     @map("results_count")
  ipAddress    String?  @map("ip_address") @db.VarChar(45)
  createdAt    DateTime @default(now()) @map("created_at")

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([createdAt])
  @@map("search_history")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  type      String   @db.VarChar(50)
  title     String   @db.VarChar(255)
  message   String   @db.Text
  data      Json?
  isRead    Boolean  @default(false) @map("is_read")
  createdAt DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
  @@map("notifications")
}

model PricingPlan {
  id            String        @id @default(cuid())
  name          String        @db.VarChar(100)
  description   String?       @db.Text
  price         Decimal       @db.Decimal(10, 2)
  billingPeriod BillingPeriod @map("billing_period")
  features      Json?
  maxListings   Int?          @map("max_listings")
  isFeatured    Boolean       @default(false) @map("is_featured")
  isActive      Boolean       @default(true) @map("is_active")
  createdAt     DateTime      @default(now()) @map("created_at")

  subscriptions UserSubscription[]

  @@index([isActive])
  @@index([isFeatured])
  @@map("pricing_plans")
}

model UserSubscription {
  id        String             @id @default(cuid())
  userId    String             @map("user_id")
  planId    String             @map("plan_id")
  status    SubscriptionStatus @default(ACTIVE)
  startDate DateTime           @map("start_date")
  endDate   DateTime           @map("end_date")
  autoRenew Boolean            @default(true) @map("auto_renew")
  createdAt DateTime           @default(now()) @map("created_at")

  user User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan PricingPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([planId])
  @@index([status])
  @@index([endDate])
  @@map("user_subscriptions")
}

model ContactForm {
  id        String   @id @default(cuid())
  name      String   @db.VarChar(100)
  email     String   @db.VarChar(255)
  phone     String?  @db.VarChar(20)
  subject   String?  @db.VarChar(255)
  message   String   @db.Text
  listingId String?  @map("listing_id")
  isRead    Boolean  @default(false) @map("is_read")
  createdAt DateTime @default(now()) @map("created_at")

  listing CarListing? @relation(fields: [listingId], references: [id], onDelete: SetNull)

  @@index([email])
  @@index([listingId])
  @@index([isRead])
  @@index([createdAt])
  @@map("contact_forms")
}

model LoanCalculation {
  id             String   @id @default(cuid())
  userId         String?  @map("user_id")
  vehiclePrice   Decimal  @map("vehicle_price") @db.Decimal(12, 2)
  downPayment    Decimal  @map("down_payment") @db.Decimal(12, 2)
  interestRate   Decimal  @map("interest_rate") @db.Decimal(5, 2)
  loanTermMonths Int      @map("loan_term_months")
  monthlyPayment Decimal  @map("monthly_payment") @db.Decimal(10, 2)
  totalInterest  Decimal  @map("total_interest") @db.Decimal(12, 2)
  totalAmount    Decimal  @map("total_amount") @db.Decimal(12, 2)
  createdAt      DateTime @default(now()) @map("created_at")

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([createdAt])
  @@map("loan_calculations")
}

model SystemSetting {
  id           String      @id @default(cuid())
  settingKey   String      @unique @map("setting_key") @db.VarChar(100)
  settingValue String?     @map("setting_value") @db.Text
  settingType  SettingType @default(STRING) @map("setting_type")
  description  String?     @db.Text
  isPublic     Boolean     @default(false) @map("is_public")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  @@index([settingKey])
  @@index([isPublic])
  @@map("system_settings")
}

model AccessoryCategory {
  id          String   @id @default(cuid())
  name        String   @unique @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  parentId    String?  @map("parent_id")
  icon        String?  @db.VarChar(100)
  isActive    Boolean  @default(true) @map("is_active")
  sortOrder   Int      @default(0) @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")

  parent      AccessoryCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: SetNull)
  children    AccessoryCategory[] @relation("CategoryHierarchy")
  accessories Accessory[]

  @@index([parentId])
  @@index([slug])
  @@index([isActive])
  @@map("accessories_categories")
}

model AccessoryBrand {
  id        String   @id @default(cuid())
  name      String   @unique @db.VarChar(100)
  logo      String?  @db.VarChar(500)
  website   String?  @db.VarChar(255)
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")

  accessories Accessory[]

  @@index([name])
  @@index([isActive])
  @@map("accessories_brands")
}

model Accessory {
  id                 String        @id @default(cuid())
  userId             String        @map("user_id")
  title              String        @db.VarChar(255)
  description        String?       @db.Text
  categoryId         String        @map("category_id")
  brandId            String?       @map("brand_id")
  sku                String?       @unique @db.VarChar(100)
  price              Decimal       @db.Decimal(10, 2)
  salePrice          Decimal?      @map("sale_price") @db.Decimal(10, 2)
  condition          ItemCondition
  stockQuantity      Int           @default(0) @map("stock_quantity")
  manageStock        Boolean       @default(true) @map("manage_stock")
  stockStatus        StockStatus   @default(INSTOCK) @map("stock_status")
  weight             Decimal?      @db.Decimal(8, 2)
  dimensions         String?       @db.VarChar(100)
  compatibilityNotes String?       @map("compatibility_notes") @db.Text
  warrantyInfo       String?       @map("warranty_info") @db.Text
  primaryImage       String?       @map("primary_image") @db.VarChar(500)
  galleryImages      Json?         @map("gallery_images")
  videos             Json?
  status             ListingStatus @default(PENDING)
  featured           Boolean       @default(false)
  viewsCount         Int           @default(0) @map("views_count")
  favoritesCount     Int           @default(0) @map("favorites_count")
  location           String?       @db.VarChar(255)
  latitude           Decimal?      @db.Decimal(10, 8)
  longitude          Decimal?      @db.Decimal(11, 8)
  contactPhone       String?       @map("contact_phone") @db.VarChar(20)
  contactEmail       String?       @map("contact_email") @db.VarChar(255)
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")

  user            User                     @relation(fields: [userId], references: [id], onDelete: Cascade)
  category        AccessoryCategory        @relation(fields: [categoryId], references: [id])
  brand           AccessoryBrand?          @relation(fields: [brandId], references: [id])
  compatibilities AccessoryCompatibility[]
  specifications  AccessorySpecification[]

  @@index([userId])
  @@index([categoryId])
  @@index([brandId])
  @@index([price])
  @@index([status])
  @@index([featured])
  @@index([latitude, longitude])
  @@map("accessories")
}

model AccessoryCompatibility {
  id          String   @id @default(cuid())
  accessoryId String   @map("accessory_id")
  makeId      String?  @map("make_id")
  modelId     String?  @map("model_id")
  yearStart   Int?     @map("year_start")
  yearEnd     Int?     @map("year_end")
  notes       String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")

  accessory Accessory @relation(fields: [accessoryId], references: [id], onDelete: Cascade)
  make      CarMake?  @relation(fields: [makeId], references: [id], onDelete: Cascade)
  model     CarModel? @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@index([accessoryId])
  @@index([makeId, modelId])
  @@map("accessories_compatibility")
}

model AccessorySpecification {
  id          String   @id @default(cuid())
  accessoryId String   @map("accessory_id")
  specName    String   @map("spec_name") @db.VarChar(100)
  specValue   String   @map("spec_value") @db.VarChar(255)
  specUnit    String?  @map("spec_unit") @db.VarChar(20)
  createdAt   DateTime @default(now()) @map("created_at")

  accessory Accessory @relation(fields: [accessoryId], references: [id], onDelete: Cascade)

  @@index([accessoryId])
  @@index([specName])
  @@map("accessories_specifications")
}
