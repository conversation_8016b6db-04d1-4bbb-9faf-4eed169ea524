import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import {
  Notification,
  NotificationCreateInput,
  NotificationPublic,
  NotificationsSelect,
} from './notification.schema';
import { PaginationOptions, PaginationResult } from 'src/common/interface';
import { NotificationsRepositoryInterface } from './notifications.repository.interface';

@Injectable()
export class NotificationsRepository
  implements NotificationsRepositoryInterface
{
  constructor(private database: DatabaseService) {}

  async create(data: NotificationCreateInput): Promise<Notification> {
    return this.database.notification.create({ data });
  }

  async find(id: string): Promise<Notification | null> {
    return this.database.notification.findUnique({
      where: { id },
    });
  }

  async findAll(
    options?: PaginationOptions,
  ): Promise<PaginationResult<Notification>> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;

    const [notifications, total] = await Promise.all([
      this.database.notification.findMany({
        skip,
        take: limit,
        select: NotificationsSelect,
      }),
      this.database.notification.count(),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      items: notifications,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  async update(id: string, data: Partial<Notification>): Promise<Notification> {
    return this.database.notification.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<Notification> {
    return this.database.notification.delete({
      where: { id },
    });
  }

  getNotificationsForUser(
    userId: string,
    options?: PaginationOptions,
  ): Promise<PaginationResult<NotificationPublic>> {
    
  }
}
