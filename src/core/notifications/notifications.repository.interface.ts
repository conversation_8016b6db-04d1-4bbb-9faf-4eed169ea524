import {
  BaseRepository,
  PaginationOptions,
  PaginationResult,
} from 'src/common/interface';
import { NotificationPublic } from './notification.schema';

export interface NotificationsRepositoryInterface
  extends BaseRepository<Notification> {
  markAsRead(id: string): Promise<Notification>;
  markAllAsRead(userId: string): Promise<Notification[]>;
  getUnreadCount(userId: string): Promise<number>;
  getNotificationsForUser(
    userId: string,
    options?: PaginationOptions,
  ): Promise<PaginationResult<NotificationPublic>>;
}
