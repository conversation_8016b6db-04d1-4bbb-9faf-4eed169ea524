import { BaseRepository } from 'src/common/interface';
import { User, UserCreateInput } from './users.schema';

export interface UserRepository extends BaseRepository<User> {
  findUserById(id: number): Promise<User>;
  findUserByPhone(phone: string): Promise<User>;
  findUserByGoogleId(googleId: string): Promise<User>;
  updateUser(id: number, data: Partial<UserCreateInput>): Promise<User>;
  deleteUser(id: number): Promise<User>;
  findUserByEmail(email: string): Promise<User>;
}
