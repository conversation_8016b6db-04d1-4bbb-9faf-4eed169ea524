import { BaseRepository } from 'src/common/interface';
import { User, UserCreateInput } from './users.schema';

export interface UserRepositoryInterface extends BaseRepository<User, string> {
  findUserById(id: string): Promise<User | null>;
  findUserByPhone(phone: string): Promise<User | null>;
  findUserByGoogleId(googleId: string): Promise<User | null>;
  updateUser(id: string, data: Partial<UserCreateInput>): Promise<User>;
  deleteUser(id: string): Promise<User>;
  findUserByEmail(email: string): Promise<User | null>;
}
