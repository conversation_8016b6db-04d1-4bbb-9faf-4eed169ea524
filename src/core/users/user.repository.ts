import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { BaseRepository } from 'src/common/interface';
import { User, UserCreateInput, UsersSelect } from './users.schema';
import {
  PaginationOptions,
  PaginationResult,
} from 'src/common/interface/pagination.interface';

@Injectable()
export class UserRepository implements BaseRepository<User> {
  constructor(private database: DatabaseService) {}

  async create(data: UserCreateInput): Promise<User> {
    return this.database.user.create({ data });
  }

  async find(id: number): Promise<User> {
    return this.database.user.findUnique({
      where: { id: BigInt(id) },
      select: UsersSelect,
    });
  }

  async findAll(options?: PaginationOptions): Promise<PaginationResult<User>> {
    const skip = (options?.page - 1) * options?.take;
    const [data, total] = await Promise.all([
      this.database.user.findMany({
        skip: options?.skip,
        take: options?.take,
      }),
      this.database.user.count(),
    ]);

    return {
      data,
      total,
      page: options?.page || 1,
      pageSize: options?.take || total,
    };
  }

  async update(id: number, data: Partial<User>): Promise<User> {
    return this.database.user.update({
      where: { id: BigInt(id) },
      data,
    });
  }

  async delete(id: number): Promise<User> {
    return this.database.user.delete({
      where: { id: BigInt(id) },
    });
  }

  async findUserById(id: number): Promise<User> {
    return this.database.user.findUnique({
      where: { id: BigInt(id) },
    });
  }

  async findUserByPhone(phone: string): Promise<User> {
    return this.database.user.findUnique({
      where: { phone },
    });
  }

  async findUserByGoogleId(googleId: string): Promise<User> {
    return this.database.user.findUnique({
      where: { googleId },
    });
  }

  async updateUser(id: number, data: Partial<UserCreateInput>): Promise<User> {
    return this.database.user.update({
      where: { id: BigInt(id) },
      data,
    });
  }

  async deleteUser(id: number): Promise<User> {
    return this.database.user.delete({
      where: { id: BigInt(id) },
    });
  }

  async findUserByEmail(email: string): Promise<User> {
    return this.database.user.findUnique({
      where: { email },
    });
  }
}
