import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { UserCreateInput } from './users.schema';

@Injectable()
export class UsersService {
  constructor(private database: DatabaseService) {}

  createUser(createUserDto: UserCreateInput) {
    try {
      return this.database.user.create({ data: createUserDto });
    } catch (error) {}
  }
}
